"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useRef, KeyboardEvent } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Loader2, ChevronLeft, ChevronRight } from "lucide-react"
import { searchChemicals, getChemicalSuggestions } from "@/lib/api"
import { Command, CommandGroup, CommandItem, CommandList } from "@/components/ui/command"
import { debounce } from "lodash"

// Define types for our data
type ChemicalRegulation = {
  id: string;
  name: string;
  shortName?: string;
  country: string;
  region: string;
  categories: string[];
  smlValue?: string;
  smlUnit?: string;
  notes?: string;
  restrictions?: string;
  additionalInfo?: Record<string, any>;
  relationId?: string;
};

type Chemical = {
  id: string;
  name: string;
  casNumber: string;
  status: string;
  riskLevel: string;
  riskDescription?: string;
  regulations: ChemicalRegulation[];
};

type ChemicalSuggestion = {
  name: string;
  casNumber: string;
};

// No filter constants needed

export default function SearchPage() {
  // Initialize with empty string to avoid hydration mismatch
  const [searchTerm, setSearchTerm] = useState("")
  const [inputValue, setInputValue] = useState("")
  const [suggestions, setSuggestions] = useState<ChemicalSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const commandListRef = useRef<HTMLDivElement>(null)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [allResults, setAllResults] = useState<Chemical[]>([])
  const itemsPerPage = 50

  // Use useEffect to load from sessionStorage after hydration
  useEffect(() => {
    const savedTerm = sessionStorage.getItem("lastSearchTerm") || ""
    if (savedTerm) {
      setSearchTerm(savedTerm)
      setInputValue(savedTerm)
    }
  }, [])

  const [searchResults, setSearchResults] = useState<Chemical[]>([])
  // Removed filter state variables
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [initialDataLoaded, setInitialDataLoaded] = useState(false)

  // Function to perform the search
  const performSearch = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get the current search term directly from state to ensure we have the latest value
      const currentSearchTerm = searchTerm;

      // Save search term to sessionStorage if it's not empty
      if (currentSearchTerm.trim()) {
        sessionStorage.setItem("lastSearchTerm", currentSearchTerm);
      } else {
        // Clear the saved search term if the current search is empty
        sessionStorage.removeItem("lastSearchTerm");
      }

      console.log('Search page: Performing search with parameters:', {
        searchTerm: currentSearchTerm || '(empty)'
      });

      // Call the API to search for chemicals
      // Always pass the searchTerm even if it's empty - this will return all chemicals when empty
      console.log('Calling API with search term:', currentSearchTerm);
      const result = await searchChemicals(
        currentSearchTerm,
        undefined,
        undefined
      );

      // Check if the search was successful
      if (!result.success) {
        console.error('Search page: Search failed with error:', result.error);
        setError(result.error || 'Failed to search chemicals. Please try again.');
        setSearchResults([]);
        setAllResults([]);
        setTotalPages(1);
        setCurrentPage(1);
        return;
      }

      // Log the search results for debugging
      console.log('Search page: Search results:', result.data.length, 'items found');

      // Check if we got any results
      if (result.data.length === 0) {
        console.log('Search page: No results found for the search criteria');
        setSearchResults([]);
        setAllResults([]);
        setTotalPages(1);
        setCurrentPage(1);

        // If this was a search with criteria and we found no results,
        // we still want to mark initialDataLoaded as true to prevent infinite loading
        if (searchTerm) {
          setInitialDataLoaded(true);
        }

        return;
      }

      // Process each chemical to ensure regulations are properly formatted
      const processedResults = result.data.map((chemical: Chemical) => {
        // Ensure regulations is always an array, even if it's undefined or null
        const regulations = Array.isArray(chemical.regulations)
          ? chemical.regulations.filter((reg: ChemicalRegulation | null | undefined) =>
              reg !== null && reg !== undefined)
          : [];

        // Return the chemical with properly formatted regulations
        return {
          ...chemical,
          regulations: regulations
        };
      });

      // Store all results for pagination
      setAllResults(processedResults);

      // Calculate total pages
      const total = Math.ceil(processedResults.length / itemsPerPage);
      setTotalPages(total);

      // Reset to first page when performing a new search
      setCurrentPage(1);

      // Get the current page of results
      const startIndex = 0;
      const endIndex = Math.min(itemsPerPage, processedResults.length);
      const currentPageResults = processedResults.slice(startIndex, endIndex);

      // Update the search results with the current page
      setSearchResults(currentPageResults);
    } catch (err) {
      console.error('Search page: Error searching chemicals:', err);
      setError('An unexpected error occurred. Please try again.');
      setSearchResults([]);
      setAllResults([]);
      setTotalPages(1);
      setCurrentPage(1);
    } finally {
      setIsLoading(false);
    }
  }, [searchTerm, itemsPerPage, setInitialDataLoaded]);

  // Function to handle page change
  const handlePageChange = useCallback((newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;

    setCurrentPage(newPage);

    // Calculate the slice of results to show
    const startIndex = (newPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, allResults.length);
    const pageResults = allResults.slice(startIndex, endIndex);

    setSearchResults(pageResults);
  }, [totalPages, allResults, itemsPerPage]);

  // Function to perform a direct search with a specific term
  const performDirectSearch = useCallback(async (term: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // Save search term to sessionStorage if it's not empty
      if (term.trim()) {
        sessionStorage.setItem("lastSearchTerm", term);
      } else {
        // Clear the saved search term if the current search is empty
        sessionStorage.removeItem("lastSearchTerm");
      }

      console.log('Search page: Performing direct search with term:', term);

      // Call the API to search for chemicals
      const result = await searchChemicals(
        term,
        undefined,
        undefined
      );

      // Check if the search was successful
      if (!result.success) {
        console.error('Search page: Direct search failed with error:', result.error);
        setError(result.error || 'Failed to search chemicals. Please try again.');
        setSearchResults([]);
        setAllResults([]);
        setTotalPages(1);
        setCurrentPage(1);
        return;
      }

      // Log the search results for debugging
      console.log('Search page: Direct search results:', result.data.length, 'items found');

      // Process the results
      const processedResults = result.data.map((chemical: Chemical) => {
        const regulations = Array.isArray(chemical.regulations)
          ? chemical.regulations.filter((reg: ChemicalRegulation | null | undefined) =>
              reg !== null && reg !== undefined)
          : [];

        return {
          ...chemical,
          regulations: regulations
        };
      });

      // Store all results for pagination
      setAllResults(processedResults);

      // Calculate total pages
      const total = Math.ceil(processedResults.length / itemsPerPage);
      setTotalPages(total);

      // Reset to first page
      setCurrentPage(1);

      // Get the current page of results
      const startIndex = 0;
      const endIndex = Math.min(itemsPerPage, processedResults.length);
      const currentPageResults = processedResults.slice(startIndex, endIndex);

      // Update the search results with the current page
      setSearchResults(currentPageResults);
    } catch (err) {
      console.error('Search page: Error in direct search:', err);
      setError('An unexpected error occurred. Please try again.');
      setSearchResults([]);
      setAllResults([]);
      setTotalPages(1);
      setCurrentPage(1);
    } finally {
      setIsLoading(false);
    }
  }, [itemsPerPage]);

  // Function to fetch chemical suggestions
  const fetchSuggestions = useCallback(
    debounce(async (query: string) => {
      if (!query || query.length < 1) {
        setSuggestions([]);
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        return;
      }

      try {
        const result = await getChemicalSuggestions(query);

        if (result.success && result.data.length > 0) {
          setSuggestions(result.data);
          setShowSuggestions(true);
          // Reset the selected index when new suggestions are loaded
          setSelectedSuggestionIndex(-1);
        } else {
          setSuggestions([]);
          setShowSuggestions(false);
          setSelectedSuggestionIndex(-1);
        }
      } catch (error) {
        console.error('Error fetching suggestions:', error);
        setSuggestions([]);
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
    }, 300),
    []
  );

  // Create a debounced version of the search function
  // This will wait 300ms after the user stops typing before performing the search
  const debouncedSearch = useCallback(
    debounce(() => {
      performSearch();
    }, 300),
    [performSearch]
  );

  // Function to load initial data when the component first mounts
  const loadInitialData = useCallback(async () => {
    try {
      // If we've already loaded initial data, don't try again
      if (initialDataLoaded) {
        console.log('Search page: Initial data already loaded, skipping');
        return;
      }

      console.log('Search page: Loading initial data');
      setIsLoading(true);

      // Call the API to get all chemicals (empty search returns all)
      const result = await searchChemicals('', undefined, undefined);

      // Mark that we've attempted to load initial data, even if it failed or returned no results
      setInitialDataLoaded(true);

      if (!result.success) {
        console.error('Search page: Failed to load initial data:', result.error);
        return;
      }

      // Process all chemicals
      const processedResults = result.data.map((chemical: Chemical) => {
        const regulations = Array.isArray(chemical.regulations)
          ? chemical.regulations.filter((reg: ChemicalRegulation | null | undefined) =>
              reg !== null && reg !== undefined)
          : [];

        return {
          ...chemical,
          regulations: regulations
        };
      });

      // Store all results for pagination
      setAllResults(processedResults);

      // Calculate total pages
      const total = Math.ceil(processedResults.length / itemsPerPage);
      setTotalPages(total);

      // Get the first page of results
      const initialData = processedResults.slice(0, itemsPerPage);
      console.log('Search page: Loaded initial data:', initialData.length, 'items out of', processedResults.length);

      // If no chemicals were found, just set empty results and don't try again
      if (initialData.length === 0) {
        console.log('Search page: No chemicals found in database');
        setSearchResults([]);
        return;
      }

      // Update the search results with the first page
      setSearchResults(initialData);
    } catch (err) {
      console.error('Search page: Error loading initial data:', err);
      // Mark as loaded even if there was an error to prevent infinite retries
      setInitialDataLoaded(true);
    } finally {
      setIsLoading(false);
    }
  }, [initialDataLoaded, itemsPerPage]);

  // Cleanup function for debounced searches
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
      fetchSuggestions.cancel();
    };
  }, [debouncedSearch, fetchSuggestions]);

  // Effect to load initial data when the component first mounts
  useEffect(() => {
    // Only attempt to load initial data once when the component mounts
    // and only if we haven't already loaded data
    if (!initialDataLoaded && !isLoading) {
      loadInitialData();
    }
  }, [loadInitialData, initialDataLoaded, isLoading]);

  // We'll handle scrolling directly in the keyboard handlers instead of using an effect



  return (
    <div className="container py-12">
      <div className="flex flex-col items-center space-y-4 text-center mb-8">
        <h1 className="text-3xl font-bold">Search & Match Chemicals</h1>
        <p className="text-muted-foreground max-w-[700px]">
          Search for chemicals by name or CAS number to check their status and relevant regulations
        </p>
      </div>

      <Card className="mb-8">
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 flex gap-2">
              <div className="relative flex-1 flex gap-2">
                <div className="relative flex-1">
                  <Input
                    placeholder="Search by chemical name or CAS number"
                    value={inputValue}
                    onChange={(e) => {
                      const value = e.target.value;
                      setInputValue(value);
                      if (value.length > 0) {
                        fetchSuggestions(value);
                      } else {
                        setShowSuggestions(false);
                        setSuggestions([]);
                      }
                    }}
                    onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
                      if (e.key === 'Enter') {
                        e.preventDefault(); // Prevent default to avoid double submission

                        // If suggestions are shown and an item is selected, use that item
                        if (showSuggestions && suggestions.length > 0 && selectedSuggestionIndex >= 0) {
                          const selectedSuggestion = suggestions[selectedSuggestionIndex];
                          const name = selectedSuggestion.name;
                          console.log('Selected suggestion with keyboard:', name);
                          setInputValue(name);
                          setSearchTerm(name);
                          setShowSuggestions(false);
                          setSelectedSuggestionIndex(-1);
                          performDirectSearch(name);
                        } else {
                          // Otherwise perform search with current input value
                          setShowSuggestions(false);
                          let trimmedValue = inputValue.trim();

                          if (trimmedValue && /\d/.test(trimmedValue)) {
                            console.log('Possible CAS number detected in search:', trimmedValue);
                          }

                          console.log('Enter key pressed, searching for:', trimmedValue || 'all chemicals');
                          setSearchTerm(trimmedValue);
                          performDirectSearch(trimmedValue);
                        }
                      } else if (e.key === 'ArrowDown') {
                        e.preventDefault(); // Prevent cursor from moving and page scrolling
                        e.stopPropagation(); // Stop event propagation to prevent page scrolling
                        console.log('Arrow down pressed, input value:', inputValue);

                        if (showSuggestions && suggestions.length > 0) {
                          console.log('Suggestions already shown, navigating down');
                          // Move selection down or wrap around to the first item
                          const newIndex = selectedSuggestionIndex < suggestions.length - 1 ? selectedSuggestionIndex + 1 : 0;
                          console.log('Moving selection down to index:', newIndex);
                          setSelectedSuggestionIndex(newIndex);

                          // Ensure the focus stays in the input field
                          if (searchInputRef.current) {
                            searchInputRef.current.focus();
                          }

                          // Wait for the DOM to update with the new selected item
                          setTimeout(() => {
                            if (commandListRef.current) {
                              // Find all items and get the one at the new index
                              const items = commandListRef.current.querySelectorAll('[cmdk-item]');
                              if (items && items.length > newIndex) {
                                const selectedItem = items[newIndex] as HTMLElement;
                                if (selectedItem) {
                                  // Calculate position within the scrollable container
                                  const container = commandListRef.current;
                                  const containerHeight = container.clientHeight;
                                  const itemHeight = selectedItem.offsetHeight;
                                  const itemTop = selectedItem.offsetTop;
                                  const scrollTop = container.scrollTop;

                                  // If item is below visible area
                                  if (itemTop + itemHeight > scrollTop + containerHeight) {
                                    container.scrollTop = itemTop + itemHeight - containerHeight;
                                  }
                                  // If item is above visible area
                                  else if (itemTop < scrollTop) {
                                    container.scrollTop = itemTop;
                                  }
                                }
                              }
                            }
                          }, 0);
                        } else if (inputValue.length > 0) {
                          console.log('Fetching suggestions for arrow down');
                          // If suggestions aren't shown but we have input, fetch and show suggestions immediately
                          // Cancel any pending debounced calls
                          fetchSuggestions.cancel();

                          // Directly call the API without debounce
                          (async () => {
                            try {
                              const result = await getChemicalSuggestions(inputValue);
                              console.log('Arrow down suggestion results:', result);

                              if (result.success && result.data.length > 0) {
                                console.log('Setting suggestions:', result.data.length);
                                setSuggestions(result.data);
                                setShowSuggestions(true);
                                // Set selection to first item
                                setSelectedSuggestionIndex(0);
                              }
                            } catch (error) {
                              console.error('Error fetching suggestions on arrow down:', error);
                            }
                          })();
                        } else {
                          // If input is empty, show all chemicals as suggestions
                          console.log('Input empty, showing all chemicals as suggestions');
                          (async () => {
                            try {
                              const result = await getChemicalSuggestions('a');  // Use 'a' to get common chemicals
                              if (result.success && result.data.length > 0) {
                                console.log('Got all chemicals:', result.data.length);
                                setSuggestions(result.data);
                                setShowSuggestions(true);
                                setSelectedSuggestionIndex(0);
                              }
                            } catch (error) {
                              console.error('Error fetching all chemicals:', error);
                            }
                          })();
                        }
                      } else if (e.key === 'ArrowUp') {
                        e.preventDefault(); // Prevent cursor from moving and page scrolling
                        e.stopPropagation(); // Stop event propagation to prevent page scrolling
                        console.log('Arrow up pressed');

                        if (showSuggestions && suggestions.length > 0) {
                          console.log('Suggestions shown, navigating up');
                          // Move selection up or wrap around to the last item
                          const newIndex = selectedSuggestionIndex > 0 ? selectedSuggestionIndex - 1 : suggestions.length - 1;
                          console.log('Moving selection up to index:', newIndex);
                          setSelectedSuggestionIndex(newIndex);

                          // Ensure the focus stays in the input field
                          if (searchInputRef.current) {
                            searchInputRef.current.focus();
                          }

                          // Wait for the DOM to update with the new selected item
                          setTimeout(() => {
                            if (commandListRef.current) {
                              // Find all items and get the one at the new index
                              const items = commandListRef.current.querySelectorAll('[cmdk-item]');
                              if (items && items.length > newIndex) {
                                const selectedItem = items[newIndex] as HTMLElement;
                                if (selectedItem) {
                                  // Calculate position within the scrollable container
                                  const container = commandListRef.current;
                                  const containerHeight = container.clientHeight;
                                  const itemHeight = selectedItem.offsetHeight;
                                  const itemTop = selectedItem.offsetTop;
                                  const scrollTop = container.scrollTop;

                                  // If item is below visible area
                                  if (itemTop + itemHeight > scrollTop + containerHeight) {
                                    container.scrollTop = itemTop + itemHeight - containerHeight;
                                  }
                                  // If item is above visible area
                                  else if (itemTop < scrollTop) {
                                    container.scrollTop = itemTop;
                                  }
                                }
                              }
                            }
                          }, 0);
                        } else if (inputValue.length > 0) {
                          // If suggestions aren't shown but we have input, fetch and show suggestions
                          console.log('Fetching suggestions for arrow up');
                          // Cancel any pending debounced calls
                          fetchSuggestions.cancel();

                          // Directly call the API without debounce
                          (async () => {
                            try {
                              const result = await getChemicalSuggestions(inputValue);

                              if (result.success && result.data.length > 0) {
                                console.log('Setting suggestions for arrow up:', result.data.length);
                                setSuggestions(result.data);
                                setShowSuggestions(true);
                                // Set selection to last item
                                setSelectedSuggestionIndex(result.data.length - 1);
                              }
                            } catch (error) {
                              console.error('Error fetching suggestions on arrow up:', error);
                            }
                          })();
                        }
                      } else if (e.key === 'Escape') {
                        // Close suggestions dropdown
                        setShowSuggestions(false);
                        setSelectedSuggestionIndex(-1);
                      }
                    }}
                    onFocus={() => inputValue.length > 0 && fetchSuggestions(inputValue)}
                    onBlur={() => setTimeout(() => {
                      // Only hide suggestions if we're not in the middle of selecting with keyboard
                      if (selectedSuggestionIndex === -1) {
                        setShowSuggestions(false);
                      }
                    }, 300)}
                    className="flex-1 pr-10"
                    ref={searchInputRef}
                  />
                  {isLoading && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    </div>
                  )}

                  {/* Chemical suggestions dropdown */}
                  {showSuggestions && suggestions.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-background border rounded-md shadow-lg">
                      <Command>
                        <CommandList ref={commandListRef} className="h-[200px] overflow-y-auto">
                          <CommandGroup heading="Suggestions">
                            {suggestions.map((suggestion, index) => (
                              <CommandItem
                                key={index}
                                onSelect={() => {
                                  const name = suggestion.name;
                                  console.log('Suggestion selected:', name);
                                  setInputValue(name);

                                  // We no longer need to remove hyphens from CAS numbers here
                                  // The backend will handle CAS numbers with or without hyphens
                                  // Just log if we detect a potential CAS number
                                  if (/\d/.test(name)) {
                                    console.log('Possible CAS number in suggestion:', name);
                                  }

                                  setSearchTerm(name);
                                  setShowSuggestions(false);
                                  setSelectedSuggestionIndex(-1);
                                  performDirectSearch(name);
                                }}
                                className={`cursor-pointer ${selectedSuggestionIndex === index ? 'bg-accent text-accent-foreground' : ''}`}
                                data-selected={selectedSuggestionIndex === index}
                                onMouseEnter={() => setSelectedSuggestionIndex(index)}
                              >
                                <Search className="h-4 w-4 mr-2 opacity-50" />
                                <div className="flex flex-col">
                                  <span>{suggestion.name}</span>
                                  <span className="text-xs text-muted-foreground">{suggestion.casNumber}</span>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </div>
                  )}
                </div>

                {/* Search button */}
                <Button
                  type="button"
                  variant="default"
                  className="gap-1"
                  disabled={isLoading}
                  onClick={() => {
                    setShowSuggestions(false);
                    // If input is empty, search for all chemicals
                    let trimmedValue = inputValue.trim();

                    // We no longer need to remove hyphens from CAS numbers here
                    // The backend will handle CAS numbers with or without hyphens
                    // Just log if we detect a potential CAS number
                    if (trimmedValue && /\d/.test(trimmedValue)) {
                      console.log('Possible CAS number detected in search button click:', trimmedValue);
                    }

                    console.log('Search button clicked, searching for:', trimmedValue || 'all chemicals');
                    setSearchTerm(trimmedValue);
                    performDirectSearch(trimmedValue);
                  }}
                >
                  <Search className="h-4 w-4" />
                  Search
                </Button>

                {/* Refresh button to download all chemicals */}
                <Button
                  type="button"
                  variant="outline"
                  className="gap-1 ml-2"
                  disabled={isLoading}
                  onClick={() => {
                    setShowSuggestions(false);
                    console.log('Refresh button clicked, downloading all chemicals');
                    setSearchTerm('');
                    setInputValue('');
                    performDirectSearch('');
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                    <path d="M21 12a9 9 0 0 1-9 9c-4.97 0-9-4.03-9-9s4.03-9 9-9h3"></path>
                    <path d="M21 3v6h-6"></path>
                    <path d="M21 9 15 3"></path>
                  </svg>
                  Refresh
                </Button>
              </div>


            </div>
          </div>
          {error && (
            <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
              {error}
            </div>
          )}
        </CardContent>
      </Card>



      {/* Admin button removed as requested */}

      <Card>
        <CardHeader>
          <CardTitle>Search Results</CardTitle>
          <CardDescription>
            {isLoading ? (
              <div className="flex items-center">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Searching...
              </div>
            ) : (
              <div>
                {`Found ${allResults.length} items`}
                {searchTerm ? ` for "${searchTerm}"` : ''}
                {allResults.length > 0 && (
                  <span className="ml-2 text-sm">
                    (Showing {(currentPage - 1) * itemsPerPage + 1}-
                    {Math.min(currentPage * itemsPerPage, allResults.length)} of {allResults.length})
                  </span>
                )}
              </div>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading && searchResults.length === 0 ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>Searching for chemicals...</span>
            </div>
          ) : searchResults.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Chemical Name</TableHead>
                  <TableHead>CAS Number</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Related Regulations</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {searchResults.map((chemical) => (
                  <TableRow key={chemical.id}>
                    <TableCell className="font-medium">
                      <Link href={`/search/details?id=${chemical.id}`} className="hover:underline hover:text-primary">
                        {chemical.name}
                      </Link>
                    </TableCell>
                    <TableCell>{chemical.casNumber}</TableCell>
                    <TableCell>{getStatusBadge(chemical.status)}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {chemical.regulations && Array.isArray(chemical.regulations) && chemical.regulations.length > 0 ? (
                          chemical.regulations.map((reg, index) => {
                            // Skip null or undefined regulations
                            if (!reg) return null;

                            console.log(`Rendering regulation ${index}:`, reg, 'shortName:', reg?.shortName);
                            return (
                              <Link
                                key={index}
                                href={`/regulations/details?id=${reg.id}`}
                                className="no-underline"
                              >
                                <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
                                  {reg.shortName || reg.name}
                                </Badge>
                              </Link>
                            );
                          })
                        ) : (
                          <span className="text-xs text-muted-foreground">No regulations</span>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <Button asChild variant="link" size="sm">
                        <Link href={`/search/details?id=${chemical.id}`}>View Details</Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                {searchTerm
                  ? "No chemicals found matching your search criteria"
                  : "Enter a search term to find chemicals"}
              </p>
            </div>
          )}

          {/* Pagination controls */}
          {searchResults.length > 0 && totalPages > 1 && (
            <div className="mt-6 flex justify-center items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1 || isLoading}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around the current page
                  let pageToShow;
                  if (totalPages <= 5) {
                    // If we have 5 or fewer pages, show all
                    pageToShow = i + 1;
                  } else if (currentPage <= 3) {
                    // If we're near the start, show first 5 pages
                    pageToShow = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    // If we're near the end, show last 5 pages
                    pageToShow = totalPages - 4 + i;
                  } else {
                    // Otherwise show 2 before and 2 after current page
                    pageToShow = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={pageToShow}
                      variant={currentPage === pageToShow ? "default" : "outline"}
                      size="sm"
                      className="w-9 h-9 p-0"
                      onClick={() => handlePageChange(pageToShow)}
                      disabled={isLoading}
                    >
                      {pageToShow}
                    </Button>
                  );
                })}

                {/* Show ellipsis if there are more pages */}
                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <span className="mx-1">...</span>
                )}

                {/* Always show the last page if we have more than 5 pages and we're not showing it already */}
                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-9 h-9 p-0"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={isLoading}
                  >
                    {totalPages}
                  </Button>
                )}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || isLoading}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

