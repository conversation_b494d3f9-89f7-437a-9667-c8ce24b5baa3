import mongoose, { Schema, Document, Model } from 'mongoose';

// Define TypeScript interfaces for the new schema
export type ListType = 'positive' | 'negative' | 'mixed' | 'unknown';

export interface IRegulationLimits {
  sml_value?: string;
  sml_unit?: string;
  max_level_percent?: number;
}

export interface ISourceInfo {
  file_name: string;
  file_type: string;
  imported_date: Date;
  row_number?: number;
  original_data?: Record<string, any>;
}

export interface IChemicalRegulation {
  regulation_id: mongoose.Types.ObjectId;
  regulation_name: string;
  regulation_type: string;
  list_type: ListType;
  country: string;
  region: string;
  limits?: IRegulationLimits;
  specific_data?: Record<string, any>;
  source_info: ISourceInfo;
  notes?: string;
  restrictions?: string;
  added_date: Date;
  updated_date: Date;
}

export interface IChemical extends Document {
  chemical_name: string;
  cas_number: string;
  alternative_names: string[];
  ec_number?: string;
  regulations: IChemicalRegulation[];
  source_files: string[];
  created_at: Date;
  updated_at: Date;
  uploadResults: mongoose.Types.ObjectId[];
  calculationResults: mongoose.Types.ObjectId[];
}

const ChemicalSchema: Schema<IChemical> = new Schema(
  {
    name: { type: String, required: true },
    casNumber: { type: String, required: true, unique: true },
    status: {
      type: String,
      enum: ['allowed', 'restricted', 'prohibited', 'unknown'],
      default: 'allowed'
    },
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'unknown'],
      default: 'low'
    },
    riskDescription: { type: String },
    chemicalRegulations: [
      { type: Schema.Types.ObjectId, ref: 'ChemicalRegulation' }
    ],
    uploadResults: [{ type: Schema.Types.ObjectId, ref: 'UploadResult' }],
    calculationResults: [
      { type: Schema.Types.ObjectId, ref: 'CalculationResult' }
    ],
  },
  {
    timestamps: true,
  }
);

// Create or reuse the Chemical model
const Chemical: Model<IChemical> =
  mongoose.models.Chemical || mongoose.model<IChemical>('Chemical', ChemicalSchema);

export default Chemical;